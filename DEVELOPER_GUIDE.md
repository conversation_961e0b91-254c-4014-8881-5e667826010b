## WorkStation Setup

Setup [Ubuntu 22.04.x LTS](https://releases.ubuntu.com/jammy/)
> Should be adapated to Windows via WSL (Windows Subsystem for Linux)

Clone Project
```
git clone https://github.com/prospr/light.git
```

Setup env
```
./scripts/setup_dev_env.sh
```

Build
```
./scripts/build.sh
```

Debug build
```
./scripts/build.sh -t x86_64 -b debug -c
```

Run
```
./scripts/run.sh
```

## Core Components

### AppController.qml
**Purpose:**  
Central application controller to manage app state, navigation, and initialization.

**How to Use:**  
Import and create a singleton or property reference. Use its properties and methods to manage screen navigation and observe state changes.

**Example:**
```qml
import UI.Core 1.0

property var appController: AppController {}

Component.onCompleted: {
    appController.initialize()
}
```

---

### ContentLoader.qml
**Purpose:**  
Dynamic loader for QML screens with robust error handling and debug output. Uses PathResolver to resolve paths across platforms.

**How to Use:**  
Instantiate and bind to your main application controller. Use `tryLoadScreen()` to load screens dynamically.

**Example:**
```qml
ContentLoader {
    id: loader
    appController: appController
    onLoadingCompleted: console.log("Loaded: " + screenName)
    onLoadingFailed: console.error("Failed: " + screenName + " - " + errorMessage)
}
```

---

### PathResolver.qml
**Purpose:**  
Singleton for resolving resource paths. Handles differences between desktop (`qrc:/`) and embedded (`file:///`) environments.

**How to Use:**  
Import as a singleton and use its methods to resolve QML or asset paths.

**Example:**
```qml
import UI.Core.PathResolver 1.0 as PathResolver

Loader {
    source: PathResolver.resolveScreenPath("HomeScreen")
}
```

---

## Theme

The `UI/Theme` directory provides centralized, reusable QML singletons for all design tokens and theming in the Prospr Light application. Import them as a namespace (recommended: `Theme`) in your QML files:

```qml
import "qrc:/UI/Theme" as Theme
```

### Colors.qml
**Purpose:**
Central color palette for the application, including brand, semantic, and neutral colors.

**How to Use:**
Reference colors as `Theme.Colors.primary`, `Theme.Colors.success`, etc.

**Example:**
```qml
Rectangle {
    color: Theme.Colors.primary // Prospr orange
}
```

---

### Radius.qml
**Purpose:**
Defines standard border radius values for UI elements, ensuring consistency across the app.

**How to Use:**
Reference as `Theme.Radius.medium`, `Theme.Radius.button`, etc.

**Example:**
```qml
Rectangle {
    radius: Theme.Radius.button // 8px
}
```

---

### Shadows.qml
**Purpose:**
Provides a simple, consistent shadow/border system for UI elevation. Uses border color instead of drop shadows for performance and compatibility.

**How to Use:**
Call `Theme.Shadows.applyElevation(target, level)` to apply a border shadow.

**Example:**
```qml
Component.onCompleted: Theme.Shadows.applyElevation(myRect, 1)
```

---

### Spacing.qml
**Purpose:**
Defines all spacing and margin values (including touch-optimized spacing) for consistent layout.

**How to Use:**
Reference as `Theme.Spacing.medium`, `Theme.Spacing.touchableMinHeight`, etc.

**Example:**
```qml
Column {
    spacing: Theme.Spacing.medium // 24px
}
```

---

### Typography.qml
**Purpose:**
Centralizes font families, weights, and font sizes for all text in the app, supporting industrial readability and brand consistency.

**How to Use:**
Reference as `Theme.Typography.primaryFontFamily`, `Theme.Typography.h1`, etc.

**Example:**
```qml
Text {
    font.family: Theme.Typography.primaryFontFamily
    font.pixelSize: Theme.Typography.h1
    font.weight: Theme.Typography.weightBold
}
```

---

### Theme.qml
**Purpose:**
Acts as a central theme controller. Exposes a `themeChanged()` signal and a `refreshTheme()` method for future runtime theming support.

**How to Use:**
Listen for `Theme.Theme.themeChanged` if you want to react to theme changes.

**Example:**
```qml
Connections {
    target: Theme.Theme
    onThemeChanged: myComponent.updateTheme()
}
```

---

## Extras

Refer to the [ARCHITECTURE.md](ARCHITECTURE.md) for more information.
Refer to context/qml_best_practices.md for more information.
Refer to context/qt_qml_doc.md for more information.

## Pull Request Process

1. Create a new branch using ticket number (ex: `git checkout -b 12345-<meaningful_name>`)
2. Make your changes
3. Commit your changes
4. Push your changes
5. Create a pull request (against master)
6. Wait for review
7. Merge pull request (SQUASH)
