<RCC>
    <qresource prefix="/">
        <file>main.qml</file>
        <file>UI/Components/errorlog/qmldir</file>
        <file>UI/Core/qmldir</file>
        <file>UI/MainLayout.qml</file>
        <file>UI/Theme/qmldir</file>
        <file>UI/Core/AppController.qml</file>
        <file>UI/Core/constants.js</file>
        <file>UI/Core/ContentLoader.qml</file>
        <file>UI/Core/DisplayConfig.qml</file>
        <file>UI/Core/FallbackScreen.qml</file>
        <file>UI/Core/NavigationManager.qml</file>
        <file>UI/Core/PathResolver.qml</file>
        <file>UI/Core/PlatformHelpers.qml</file>
        <file>UI/Theme/Colors.qml</file>
        <file>UI/Theme/Fonts.qml</file>
        <file>UI/Theme/Radius.qml</file>
        <file>UI/Theme/Shadows.qml</file>
        <file>UI/Theme/Spacing.qml</file>
        <file>UI/Theme/Theme.qml</file>
        <file>UI/Theme/Typography.qml</file>
        <file>UI/Assets/backgrounds/home-background.png</file>
        <file>UI/Assets/backgrounds/login-background.png</file>
        <file>UI/Assets/backgrounds/login-background.svg</file>
        <file>UI/Assets/backgrounds/system-background.png</file>
        <file>UI/Assets/backgrounds/system-background.svg</file>
        <file>UI/Assets/home_icon.qml</file>
        <file>UI/Assets/icons/add-user.svg</file>
        <file>UI/Assets/icons/back.svg</file>
        <file>UI/Assets/icons/brightness.svg</file>
        <file>UI/Assets/icons/card-status-circles.svg</file>
        <file>UI/Assets/icons/checkmark-language.svg</file>
        <file>UI/Assets/icons/checkmark.svg</file>
        <file>UI/Assets/icons/chevron-down.svg</file>
        <file>UI/Assets/icons/copy.svg</file>
        <file>UI/Assets/icons/create.svg</file>
        <file>UI/Assets/icons/edit.svg</file>
        <file>UI/Assets/icons/error_clear.svg</file>
        <file>UI/Assets/icons/error_reset.svg</file>
        <file>UI/Assets/icons/forbidden.svg</file>
        <file>UI/Assets/icons/home-small.svg</file>
        <file>UI/Assets/icons/home.svg</file>
        <file>UI/Assets/icons/left-arrow-black.svg</file>
        <file>UI/Assets/icons/left-arrow.svg</file>
        <file>UI/Assets/icons/logout.svg</file>
        <file>UI/Assets/icons/minus.svg</file>
        <file>UI/Assets/icons/night-mode.svg</file>
        <file>UI/Assets/icons/plus.svg</file>
        <file>UI/Assets/icons/power-button-background.svg</file>
        <file>UI/Assets/icons/power-button-hue.svg</file>
        <file>UI/Assets/icons/power-button.svg</file>
        <file>UI/Assets/icons/print.svg</file>
        <file>UI/Assets/icons/reload.svg</file>
        <file>UI/Assets/icons/right-arrow-white.svg</file>
        <file>UI/Assets/icons/save-changes.svg</file>
        <file>UI/Assets/icons/select.svg</file>
        <file>UI/Assets/icons/service.svg</file>
        <file>UI/Assets/icons/stop.svg</file>
        <file>UI/Assets/icons/system.svg</file>
        <file>UI/Assets/icons/Trash.svg</file>
        <file>UI/Assets/icons/user-circle.svg</file>
        <file>UI/Assets/icons/user.svg</file>
        <file>UI/Assets/icons/warning-grey.svg</file>
        <file>UI/Assets/icons/warning-yellow.svg</file>
        <file>UI/Assets/Images/add-circle.png</file>
        <file>UI/Assets/Images/arrow.png</file>
        <file>UI/Assets/Images/barcode.png</file>
        <file>UI/Assets/Images/batch-code.png</file>
        <file>UI/Assets/Images/bold.png</file>
        <file>UI/Assets/Images/calculator.png</file>
        <file>UI/Assets/Images/calendar.png</file>
        <file>UI/Assets/Images/check.png</file>
        <file>UI/Assets/Images/dropdown-arrow.png</file>
        <file>UI/Assets/Images/edit-rectangle-black.png</file>
        <file>UI/Assets/Images/edit-rectangle.png</file>
        <file>UI/Assets/Images/exchange.png</file>
        <file>UI/Assets/Images/flag-es.png</file>
        <file>UI/Assets/Images/flag-us.png</file>
        <file>UI/Assets/Images/function.png</file>
        <file>UI/Assets/Images/half.png</file>
        <file>UI/Assets/Images/hand-touch.png</file>
        <file>UI/Assets/Images/information.png</file>
        <file>UI/Assets/Images/insert.png</file>
        <file>UI/Assets/Images/lab_background.jpg</file>
        <file>UI/Assets/Images/language-circle.png</file>
        <file>UI/Assets/Images/menu.png</file>
        <file>UI/Assets/Images/messages.png</file>
        <file>UI/Assets/Images/metering.png</file>
        <file>UI/Assets/Images/NumberCircleOne.png</file>
        <file>UI/Assets/Images/phase.png</file>
        <file>UI/Assets/Images/qr-code-02.png</file>
        <file>UI/Assets/Images/reload.png</file>
        <file>UI/Assets/Images/service-mode.png</file>
        <file>UI/Assets/Images/shift.png</file>
        <file>UI/Assets/Images/status.png</file>
        <file>UI/Assets/Images/text.png</file>
        <file>UI/Assets/Images/trash-black.png</file>
        <file>UI/Assets/Images/trash.png</file>
        <file>UI/Assets/Images/usb.png</file>
        <file>UI/Assets/Images/user-tick.png</file>
        <file>UI/Assets/logos/prospr-logo.png</file>
        <file>UI/Assets/logos/prospr-logo.svg</file>
        <file>UI/Assets/placeholder_barcode.qml</file>
        <file>UI/Assets/power_icon.qml</file>
        <file>UI/Assets/print_icon.png</file>
        <file>UI/Assets/prospr_logo.qml</file>
        <file>UI/Assets/service_icon.png</file>
        <file>UI/Assets/ServiceToolsIcon.qml</file>
        <file>UI/Assets/settings_icon.png</file>
        <file>UI/Assets/SettingsGearIcon.qml</file>
        <file>UI/Assets/SvgIcon.qml</file>
        <file>UI/Assets/user_icon.qml</file>
        <file>UI/Assets/warning_icon.qml</file>
        <file>UI/Assets/warning_triangle.png</file>
        <file>UI/Assets/WarningTriangleIcon.qml</file>
        <file>UI/Screens/CreateEditMessage.qml</file>
        <file>UI/Screens/ErrorLog.qml</file>
        <file>UI/Screens/FileManager.qml</file>
        <file>UI/Screens/Home.qml</file>
        <file>UI/Screens/InsertData.qml</file>
        <file>UI/Screens/insertData/BarcodeInsert.qml</file>
        <file>UI/Screens/insertData/baseInsert/LeftPanelBarcode.qml</file>
        <file>UI/Screens/insertData/baseInsert/LeftPanelBatch.qml</file>
        <file>UI/Screens/insertData/baseInsert/LeftPanelMetering.qml</file>
        <file>UI/Screens/insertData/baseInsert/LeftPanelShift.qml</file>
        <file>UI/Screens/insertData/baseInsert/PrintLayoutBase.qml</file>
        <file>UI/Screens/insertData/baseInsert/TopPanelBarcode.qml</file>
        <file>UI/Screens/insertData/BatchCodeInsert.qml</file>
        <file>UI/Screens/insertData/MeteringInsert.qml</file>
        <file>UI/Screens/insertData/ShiftInsert.qml</file>
        <file>UI/Screens/insertData/TextInsert.qml</file>
        <file>UI/Screens/insertData/textInsert/LeftPanelInsert.qml</file>
        <file>UI/Screens/insertData/textInsert/PrintLayoutInsert.qml</file>
        <file>UI/Screens/insertData/textInsert/RightPanelInsert.qml</file>
        <file>UI/Screens/insertData/textInsert/TopPanelInsert.qml</file>
        <file>UI/Screens/insertDataCounter/CounterLayout.qml</file>
        <file>UI/Screens/insertDataCounter/LeftPanelCounter.qml</file>
        <file>UI/Screens/insertDataCounter/RightPanelCounter.qml</file>
        <file>UI/Screens/insertDataCounter/TopPanelCounter.qml</file>
        <file>UI/Screens/insertDataDateTime/BottomPanelJulian.qml</file>
        <file>UI/Screens/insertDataDateTime/BottomPanelOverride.qml</file>
        <file>UI/Screens/insertDataDateTime/DateTimeLayout.qml</file>
        <file>UI/Screens/insertDataDateTime/MidPanelDateTime.qml</file>
        <file>UI/Screens/insertDataDateTime/TopPanelDateTime.qml</file>
        <file>UI/Screens/InsertText.qml</file>
        <file>UI/Screens/LogIn.qml</file>
        <file>UI/Screens/login/LoginCard.qml</file>
        <file>UI/Screens/Print.qml</file>
        <file>UI/Screens/print/PrintLayout.qml</file>
        <file>UI/Screens/PrintCreate.qml</file>
        <file>UI/Screens/printCreate/LeftPanelCreate.qml</file>
        <file>UI/Screens/printCreate/PrintLayoutCreate.qml</file>
        <file>UI/Screens/printCreate/RightPanelCreate.qml</file>
        <file>UI/Screens/PrintCreateUSB.qml</file>
        <file>UI/Screens/printCreateUSB/LeftPanelUSB.qml</file>
        <file>UI/Screens/printCreateUSB/PrintLayoutUSB.qml</file>
        <file>UI/Screens/printCreateUSB/TopPanelUSB.qml</file>
        <file>UI/Screens/PrinterSettings.qml</file>
        <file>UI/Screens/PrintSelectSettings.qml</file>
        <file>UI/Screens/printSelectSettings/PrintLayoutSelectSettings.qml</file>
        <file>UI/Screens/printSelectSettings/TopPanelSelectSettings.qml</file>
        <file>UI/Screens/PrintSettings.qml</file>
        <file>UI/Screens/printSettings/LeftPanelSettings.qml</file>
        <file>UI/Screens/printSettings/PrintLayoutSettings.qml</file>
        <file>UI/Screens/printSettings/RightPanelSettings.qml</file>
        <file>UI/Screens/printSettings/TopPanelSettings.qml</file>
        <file>UI/Screens/PrintUsb.qml</file>
        <file>UI/Screens/Service.qml</file>
        <file>UI/Screens/service/Function.qml</file>
        <file>UI/Screens/service/Phase.qml</file>
        <file>UI/Screens/service/ResetMaintenance.qml</file>
        <file>UI/Screens/service/Service.qml</file>
        <file>UI/Screens/service/Status.qml</file>
        <file>UI/Screens/Settings.qml</file>
        <file>UI/Screens/settings/AddUser.qml</file>
        <file>UI/Screens/settings/DateTime.qml</file>
        <file>UI/Screens/settings/Language.qml</file>
        <file>UI/Screens/settings/Other.qml</file>
        <file>UI/Screens/settings/QRData.qml</file>
        <file>UI/Screens/settings/SystemInfo.qml</file>
        <file>UI/Screens/settings/Upgrade.qml</file>
        <file>UI/Screens/settings/UserPermissions.qml</file>
        <file>UI/Screens/settings/Users.qml</file>
        <file>UI/Components/BlurOverlay.qml</file>
        <file>UI/Components/BottomBar.qml</file>
        <file>UI/Components/bottombar/BottomBarConfigs.js</file>
        <file>UI/Components/CardButton.qml</file>
        <file>UI/Components/CardSpinBox.qml</file>
        <file>UI/Components/CardStatus.qml</file>
        <file>UI/Components/CardSwitch.qml</file>
        <file>UI/Components/CheckBoxControl.qml</file>
        <file>UI/Components/ComboBoxControl.qml</file>
        <file>UI/Components/CustomSwitchButton.qml</file>
        <file>UI/Components/DefaultButton.qml</file>
        <file>UI/Components/DropShadow.qml</file>
        <file>UI/Components/DropShadowEffect.qml</file>
        <file>UI/Components/errorlog/ErrorLogControls.qml</file>
        <file>UI/Components/errorlog/ErrorLogPanel.qml</file>
        <file>UI/Components/LeftPanelButton.qml</file>
        <file>UI/Components/LightInput.qml</file>
        <file>UI/Components/PrintControlsComponent.qml</file>
        <file>UI/Components/PrinterStatusComponent.qml</file>
        <file>UI/Components/PrintPreviewPanel.qml</file>
        <file>UI/Components/ProgressDialog.qml</file>
        <file>UI/Components/ProsprButton.qml</file>
        <file>UI/Components/ProsprTextField.qml</file>
        <file>UI/Components/QPopup.qml</file>
        <file>UI/Components/ScrollableContainer.qml</file>
        <file>UI/Components/ShutdownDialog.qml</file>
        <file>UI/Components/SquareButton.qml</file>
        <file>UI/Components/SwitchControl.qml</file>
        <file>UI/Components/SwitchIndicator.qml</file>
        <file>UI/Components/TestPrintMode.qml</file>
        <file>UI/Components/TimeHandler.qml</file>
        <file>UI/Components/TitledFrame.qml</file>
        <file>UI/Components/ToastPopup.qml</file>
    </qresource>
</RCC>
